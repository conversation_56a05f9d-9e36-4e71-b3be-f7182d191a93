-- WordPress to PostgreSQL Migration Schema
-- Generated automatically, excluding wp_users and wp_usermeta

-- Table: wp_actionscheduler_actions
DROP TABLE IF EXISTS wp_actionscheduler_actions CASCADE;
CREATE TABLE wp_actionscheduler_actions (
    action_id BIGSERIAL NOT NULL,
    hook VARCHAR(191) NOT NULL,
    status VARCHAR(20) NOT NULL,
    scheduled_date_gmt TIMESTAMP,
    scheduled_date_local TIMESTAMP,
    args VARCHAR(191) NULL DEFAULT NULL,
    schedule TEXT NULL DEFAULT NULL,
    group_id BIGINT NOT NULL DEFAULT 0,
    attempts INTEGER NOT NULL DEFAULT 0,
    last_attempt_gmt TIMESTAMP,
    last_attempt_local TIMESTAMP,
    claim_id BIGINT NOT NULL DEFAULT 0,
    extended_args VARCHAR(8000) NULL DEFAULT NULL,
    priority SMALLINT NOT NULL DEFAULT 10,
    PRIMARY KEY (action_id)
);

CREATE INDEX hook ON wp_actionscheduler_actions (hook);
CREATE INDEX status ON wp_actionscheduler_actions (status);
CREATE INDEX scheduled_date_gmt ON wp_actionscheduler_actions (scheduled_date_gmt);
CREATE INDEX args ON wp_actionscheduler_actions (args);
CREATE INDEX group_id ON wp_actionscheduler_actions (group_id);
CREATE INDEX last_attempt_gmt ON wp_actionscheduler_actions (last_attempt_gmt);
CREATE INDEX claim_id_status_scheduled_date_gmt ON wp_actionscheduler_actions (claim_id,status,scheduled_date_gmt);
CREATE INDEX hook_status_scheduled_date_gmt ON wp_actionscheduler_actions (hook(163);
CREATE INDEX status_scheduled_date_gmt ON wp_actionscheduler_actions (status,scheduled_date_gmt);
CREATE INDEX claim_id_status_priority_scheduled_date_gmt ON wp_actionscheduler_actions (claim_id,status,priority,scheduled_date_gmt);
CREATE INDEX status_last_attempt_gmt ON wp_actionscheduler_actions (status,last_attempt_gmt);
CREATE INDEX status_claim_id ON wp_actionscheduler_actions (status,claim_id);

-- Table: wp_actionscheduler_claims
DROP TABLE IF EXISTS wp_actionscheduler_claims CASCADE;
CREATE TABLE wp_actionscheduler_claims (
    claim_id BIGSERIAL NOT NULL,
    date_created_gmt TIMESTAMP,
    PRIMARY KEY (claim_id)
);

CREATE INDEX date_created_gmt ON wp_actionscheduler_claims (date_created_gmt);

-- Table: wp_actionscheduler_groups
DROP TABLE IF EXISTS wp_actionscheduler_groups CASCADE;
CREATE TABLE wp_actionscheduler_groups (
    group_id BIGSERIAL NOT NULL,
    slug VARCHAR(255) NOT NULL,
    PRIMARY KEY (group_id)
);

CREATE INDEX slug ON wp_actionscheduler_groups (slug(191);

-- Table: wp_actionscheduler_logs
DROP TABLE IF EXISTS wp_actionscheduler_logs CASCADE;
CREATE TABLE wp_actionscheduler_logs (
    log_id BIGSERIAL NOT NULL,
    action_id BIGINT NOT NULL,
    message TEXT NOT NULL,
    log_date_gmt TIMESTAMP,
    log_date_local TIMESTAMP,
    PRIMARY KEY (log_id)
);

CREATE INDEX action_id ON wp_actionscheduler_logs (action_id);
CREATE INDEX log_date_gmt ON wp_actionscheduler_logs (log_date_gmt);

-- Table: wp_commentmeta
DROP TABLE IF EXISTS wp_commentmeta CASCADE;
CREATE TABLE wp_commentmeta (
    meta_id BIGSERIAL NOT NULL,
    comment_id BIGINT NOT NULL DEFAULT 0,
    meta_key VARCHAR(255) NULL DEFAULT NULL,
    meta_value TEXT NULL DEFAULT NULL,
    PRIMARY KEY (meta_id)
);

CREATE INDEX comment_id ON wp_commentmeta (comment_id);
CREATE INDEX meta_key ON wp_commentmeta (meta_key(191);

-- Table: wp_comments
DROP TABLE IF EXISTS wp_comments CASCADE;
CREATE TABLE wp_comments (
    comment_ID BIGSERIAL NOT NULL,
    comment_post_ID BIGINT NOT NULL DEFAULT 0,
    comment_author TINYTEXT NOT NULL,
    comment_author_email VARCHAR(100) NOT NULL DEFAULT '',
    comment_author_url VARCHAR(200) NOT NULL DEFAULT '',
    comment_author_IP VARCHAR(100) NOT NULL DEFAULT '',
    comment_date TIMESTAMP NOT NULL,
    comment_date_gmt TIMESTAMP NOT NULL,
    comment_content TEXT NOT NULL,
    comment_karma INTEGER NOT NULL DEFAULT 0,
    comment_approved VARCHAR(20) NOT NULL DEFAULT '1',
    comment_agent VARCHAR(255) NOT NULL DEFAULT '',
    comment_type VARCHAR(20) NOT NULL DEFAULT 'comment',
    comment_parent BIGINT NOT NULL DEFAULT 0,
    user_id BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (comment_ID)
);

CREATE INDEX comment_post_ID ON wp_comments (comment_post_ID);
CREATE INDEX comment_approved_date_gmt ON wp_comments (comment_approved,comment_date_gmt);
CREATE INDEX comment_date_gmt ON wp_comments (comment_date_gmt);
CREATE INDEX comment_parent ON wp_comments (comment_parent);
CREATE INDEX comment_author_email ON wp_comments (comment_author_email(10);

-- Table: wp_links
DROP TABLE IF EXISTS wp_links CASCADE;
CREATE TABLE wp_links (
    link_id BIGSERIAL NOT NULL,
    link_url VARCHAR(255) NOT NULL DEFAULT '',
    link_name VARCHAR(255) NOT NULL DEFAULT '',
    link_image VARCHAR(255) NOT NULL DEFAULT '',
    link_target VARCHAR(25) NOT NULL DEFAULT '',
    link_description VARCHAR(255) NOT NULL DEFAULT '',
    link_visible VARCHAR(20) NOT NULL DEFAULT 'Y',
    link_owner BIGINT NOT NULL DEFAULT 1,
    link_rating INTEGER NOT NULL DEFAULT 0,
    link_updated TIMESTAMP NOT NULL,
    link_rel VARCHAR(255) NOT NULL DEFAULT '',
    link_notes TEXT NOT NULL,
    link_rss VARCHAR(255) NOT NULL DEFAULT '',
    PRIMARY KEY (link_id)
);

CREATE INDEX link_visible ON wp_links (link_visible);

-- Table: wp_litespeed_avatar
DROP TABLE IF EXISTS wp_litespeed_avatar CASCADE;
CREATE TABLE wp_litespeed_avatar (
    id BIGSERIAL NOT NULL,
    url VARCHAR(1000) NOT NULL DEFAULT '',
    md5 VARCHAR(128) NOT NULL DEFAULT '',
    dateline INTEGER NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX md5 ON wp_litespeed_avatar (md5);
CREATE INDEX dateline ON wp_litespeed_avatar (dateline);

-- Table: wp_litespeed_img_optming
DROP TABLE IF EXISTS wp_litespeed_img_optming CASCADE;
CREATE TABLE wp_litespeed_img_optming (
    id BIGSERIAL NOT NULL,
    post_id BIGINT NOT NULL DEFAULT 0,
    optm_status SMALLINT NOT NULL DEFAULT 0,
    src VARCHAR(1000) NOT NULL DEFAULT '',
    server_info TEXT NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX post_id ON wp_litespeed_img_optming (post_id);
CREATE INDEX optm_status ON wp_litespeed_img_optming (optm_status);
CREATE INDEX src ON wp_litespeed_img_optming (src(191);

-- Table: wp_litespeed_url
DROP TABLE IF EXISTS wp_litespeed_url CASCADE;
CREATE TABLE wp_litespeed_url (
    id BIGSERIAL NOT NULL,
    url VARCHAR(500) NOT NULL,
    cache_tags VARCHAR(1000) NOT NULL DEFAULT '',
    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX url ON wp_litespeed_url (url(191);
CREATE INDEX cache_tags ON wp_litespeed_url (cache_tags(191);

-- Table: wp_litespeed_url_file
DROP TABLE IF EXISTS wp_litespeed_url_file CASCADE;
CREATE TABLE wp_litespeed_url_file (
    id BIGSERIAL NOT NULL,
    url_id BIGINT NOT NULL,
    vary VARCHAR(32) NOT NULL DEFAULT '' COMMENT 'md5 of final vary',
    filename VARCHAR(32) NOT NULL DEFAULT '' COMMENT 'md5 of file content',
    type SMALLINT NOT NULL,
    mobile SMALLINT NOT NULL,
    webp SMALLINT NOT NULL,
    expired INTEGER NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

CREATE INDEX filename ON wp_litespeed_url_file (filename);
CREATE INDEX type ON wp_litespeed_url_file (type);
CREATE INDEX url_id_2 ON wp_litespeed_url_file (url_id,vary,type);
CREATE INDEX filename_2 ON wp_litespeed_url_file (filename,expired);
CREATE INDEX url_id ON wp_litespeed_url_file (url_id,expired);

-- Table: wp_options
DROP TABLE IF EXISTS wp_options CASCADE;
CREATE TABLE wp_options (
    option_id BIGSERIAL NOT NULL,
    option_name VARCHAR(191) NOT NULL DEFAULT '',
    option_value TEXT NOT NULL,
    autoload VARCHAR(20) NOT NULL DEFAULT 'yes',
    PRIMARY KEY (option_id)
);

CREATE UNIQUE INDEX option_name ON wp_options (option_name);
CREATE INDEX autoload ON wp_options (autoload);

-- Table: wp_postmeta
DROP TABLE IF EXISTS wp_postmeta CASCADE;
CREATE TABLE wp_postmeta (
    meta_id BIGSERIAL NOT NULL,
    post_id BIGINT NOT NULL DEFAULT 0,
    meta_key VARCHAR(255) NULL DEFAULT NULL,
    meta_value TEXT NULL DEFAULT NULL,
    PRIMARY KEY (meta_id)
);

CREATE INDEX post_id ON wp_postmeta (post_id);
CREATE INDEX meta_key ON wp_postmeta (meta_key(191);

-- Table: wp_posts
DROP TABLE IF EXISTS wp_posts CASCADE;
CREATE TABLE wp_posts (
    ID BIGSERIAL NOT NULL,
    post_author BIGINT NOT NULL DEFAULT 0,
    post_date TIMESTAMP NOT NULL,
    post_date_gmt TIMESTAMP NOT NULL,
    post_content TEXT NOT NULL,
    post_title TEXT NOT NULL,
    post_excerpt TEXT NOT NULL,
    post_status VARCHAR(20) NOT NULL DEFAULT 'publish',
    comment_status VARCHAR(20) NOT NULL DEFAULT 'open',
    ping_status VARCHAR(20) NOT NULL DEFAULT 'open',
    post_password VARCHAR(255) NOT NULL DEFAULT '',
    post_name VARCHAR(200) NOT NULL DEFAULT '',
    to_ping TEXT NOT NULL,
    pinged TEXT NOT NULL,
    post_modified TIMESTAMP NOT NULL,
    post_modified_gmt TIMESTAMP NOT NULL,
    post_content_filtered TEXT NOT NULL,
    post_parent BIGINT NOT NULL DEFAULT 0,
    guid VARCHAR(255) NOT NULL DEFAULT '',
    menu_order INTEGER NOT NULL DEFAULT 0,
    post_type VARCHAR(20) NOT NULL DEFAULT 'post',
    post_mime_type VARCHAR(100) NOT NULL DEFAULT '',
    comment_count BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (ID)
);

CREATE INDEX post_name ON wp_posts (post_name(191);
CREATE INDEX type_status_date ON wp_posts (post_type,post_status,post_date,ID);
CREATE INDEX post_parent ON wp_posts (post_parent);
CREATE INDEX post_author ON wp_posts (post_author);

-- Table: wp_rank_math_404_logs
DROP TABLE IF EXISTS wp_rank_math_404_logs CASCADE;
CREATE TABLE wp_rank_math_404_logs (
    id BIGSERIAL NOT NULL,
    uri VARCHAR(255) NOT NULL,
    accessed TIMESTAMP NOT NULL,
    times_accessed BIGINT NOT NULL DEFAULT 1,
    referer VARCHAR(255) NOT NULL DEFAULT '',
    user_agent VARCHAR(255) NOT NULL DEFAULT '',
    PRIMARY KEY (id)
);

CREATE INDEX uri ON wp_rank_math_404_logs (uri(191);

-- Table: wp_rank_math_analytics_gsc
DROP TABLE IF EXISTS wp_rank_math_analytics_gsc CASCADE;
CREATE TABLE wp_rank_math_analytics_gsc (
    id BIGSERIAL NOT NULL,
    created TIMESTAMP NOT NULL,
    query VARCHAR(1000) NOT NULL,
    page VARCHAR(500) NOT NULL,
    clicks MEDIUMINT(6) NOT NULL,
    impressions MEDIUMINT(6) NOT NULL,
    position DOUBLE PRECISION NOT NULL,
    ctr DOUBLE PRECISION NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX analytics_query ON wp_rank_math_analytics_gsc (query(190);
CREATE INDEX analytics_page ON wp_rank_math_analytics_gsc (page(190);
CREATE INDEX clicks ON wp_rank_math_analytics_gsc (clicks);
CREATE INDEX rank_position ON wp_rank_math_analytics_gsc (position);

-- Table: wp_rank_math_analytics_inspections
DROP TABLE IF EXISTS wp_rank_math_analytics_inspections CASCADE;
CREATE TABLE wp_rank_math_analytics_inspections (
    id BIGSERIAL NOT NULL,
    page VARCHAR(500) NOT NULL,
    created TIMESTAMP NOT NULL,
    index_verdict VARCHAR(64) NOT NULL,
    indexing_state VARCHAR(64) NOT NULL,
    coverage_state TEXT NOT NULL,
    page_fetch_state VARCHAR(64) NOT NULL,
    robots_txt_state VARCHAR(64) NOT NULL,
    rich_results_verdict VARCHAR(64) NOT NULL,
    rich_results_items TEXT NOT NULL,
    last_crawl_time TIMESTAMP NOT NULL,
    crawled_as VARCHAR(64) NOT NULL,
    google_canonical TEXT NOT NULL,
    user_canonical TEXT NOT NULL,
    sitemap TEXT NOT NULL,
    referring_urls TEXT NOT NULL,
    raw_api_response TEXT NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX analytics_object_page ON wp_rank_math_analytics_inspections (page(190);
CREATE INDEX created ON wp_rank_math_analytics_inspections (created);
CREATE INDEX index_verdict ON wp_rank_math_analytics_inspections (index_verdict);
CREATE INDEX page_fetch_state ON wp_rank_math_analytics_inspections (page_fetch_state);
CREATE INDEX robots_txt_state ON wp_rank_math_analytics_inspections (robots_txt_state);
CREATE INDEX rich_results_verdict ON wp_rank_math_analytics_inspections (rich_results_verdict);

-- Table: wp_rank_math_analytics_objects
DROP TABLE IF EXISTS wp_rank_math_analytics_objects CASCADE;
CREATE TABLE wp_rank_math_analytics_objects (
    id BIGSERIAL NOT NULL,
    created TIMESTAMP NOT NULL,
    title TEXT NOT NULL,
    page VARCHAR(500) NOT NULL,
    object_type VARCHAR(100) NOT NULL,
    object_subtype VARCHAR(100) NOT NULL,
    object_id BIGINT NOT NULL,
    primary_key VARCHAR(255) NOT NULL,
    seo_score SMALLINT NOT NULL DEFAULT 0,
    page_score SMALLINT NOT NULL DEFAULT 0,
    is_indexable BOOLEAN NOT NULL DEFAULT 1,
    schemas_in_use VARCHAR(500) NULL DEFAULT NULL,
    desktop_interactive DOUBLE PRECISION DEFAULT 0,
    desktop_pagescore DOUBLE PRECISION DEFAULT 0,
    mobile_interactive DOUBLE PRECISION DEFAULT 0,
    mobile_pagescore DOUBLE PRECISION DEFAULT 0,
    pagespeed_refreshed TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX analytics_object_page ON wp_rank_math_analytics_objects (page(190);

-- Table: wp_rank_math_internal_links
DROP TABLE IF EXISTS wp_rank_math_internal_links CASCADE;
CREATE TABLE wp_rank_math_internal_links (
    id BIGSERIAL NOT NULL,
    url VARCHAR(255) NOT NULL,
    post_id BIGINT NOT NULL,
    target_post_id BIGINT NOT NULL,
    type VARCHAR(8) NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX link_direction ON wp_rank_math_internal_links (post_id,type);
CREATE INDEX target_post_id ON wp_rank_math_internal_links (target_post_id);

-- Table: wp_rank_math_internal_meta
DROP TABLE IF EXISTS wp_rank_math_internal_meta CASCADE;
CREATE TABLE wp_rank_math_internal_meta (
    object_id BIGINT NOT NULL,
    internal_link_count INTEGER DEFAULT 0,
    external_link_count INTEGER DEFAULT 0,
    incoming_link_count INTEGER DEFAULT 0,
    PRIMARY KEY (object_id)
);


-- Table: wp_rank_math_redirections
DROP TABLE IF EXISTS wp_rank_math_redirections CASCADE;
CREATE TABLE wp_rank_math_redirections (
    id BIGSERIAL NOT NULL,
    sources TEXT NOT NULL,
    url_to TEXT NOT NULL,
    header_code SMALLINT(4) NOT NULL,
    hits BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(25) NOT NULL DEFAULT 'active',
    created TIMESTAMP NOT NULL,
    updated TIMESTAMP NOT NULL,
    last_accessed TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX status ON wp_rank_math_redirections (status);

-- Table: wp_rank_math_redirections_cache
DROP TABLE IF EXISTS wp_rank_math_redirections_cache CASCADE;
CREATE TABLE wp_rank_math_redirections_cache (
    id BIGSERIAL NOT NULL,
    from_url TEXT CHARACTER SET UTF8MB4 COLLATE UTF8MB4_BIN NOT NULL,
    redirection_id BIGINT NOT NULL,
    object_id BIGINT NOT NULL DEFAULT 0,
    object_type VARCHAR(10) NOT NULL DEFAULT 'post',
    is_redirected BOOLEAN NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

CREATE INDEX redirection_id ON wp_rank_math_redirections_cache (redirection_id);

-- Table: wp_term_relationships
DROP TABLE IF EXISTS wp_term_relationships CASCADE;
CREATE TABLE wp_term_relationships (
    object_id BIGINT NOT NULL DEFAULT 0,
    term_taxonomy_id BIGINT NOT NULL DEFAULT 0,
    term_order INTEGER NOT NULL DEFAULT 0,
    PRIMARY KEY (object_id,term_taxonomy_id)
);

CREATE INDEX term_taxonomy_id ON wp_term_relationships (term_taxonomy_id);

-- Table: wp_term_taxonomy
DROP TABLE IF EXISTS wp_term_taxonomy CASCADE;
CREATE TABLE wp_term_taxonomy (
    term_taxonomy_id BIGSERIAL NOT NULL,
    term_id BIGINT NOT NULL DEFAULT 0,
    taxonomy VARCHAR(32) NOT NULL DEFAULT '',
    description TEXT NOT NULL,
    parent BIGINT NOT NULL DEFAULT 0,
    count BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (term_taxonomy_id)
);

CREATE UNIQUE INDEX term_id_taxonomy ON wp_term_taxonomy (term_id,taxonomy);
CREATE INDEX taxonomy ON wp_term_taxonomy (taxonomy);

-- Table: wp_termmeta
DROP TABLE IF EXISTS wp_termmeta CASCADE;
CREATE TABLE wp_termmeta (
    meta_id BIGSERIAL NOT NULL,
    term_id BIGINT NOT NULL DEFAULT 0,
    meta_key VARCHAR(255) NULL DEFAULT NULL,
    meta_value TEXT NULL DEFAULT NULL,
    PRIMARY KEY (meta_id)
);

CREATE INDEX term_id ON wp_termmeta (term_id);
CREATE INDEX meta_key ON wp_termmeta (meta_key(191);

-- Table: wp_terms
DROP TABLE IF EXISTS wp_terms CASCADE;
CREATE TABLE wp_terms (
    term_id BIGSERIAL NOT NULL,
    name VARCHAR(200) NOT NULL DEFAULT '',
    slug VARCHAR(200) NOT NULL DEFAULT '',
    term_group BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (term_id)
);

CREATE INDEX slug ON wp_terms (slug(191);
CREATE INDEX name ON wp_terms (name(191);

-- Table: wp_wpfm_backup
DROP TABLE IF EXISTS wp_wpfm_backup CASCADE;
CREATE TABLE wp_wpfm_backup (
    id SERIAL NOT NULL,
    backup_name TEXT NULL DEFAULT NULL,
    backup_date TEXT NULL DEFAULT NULL,
    PRIMARY KEY (id)
);


-- Table: wp_wpforms_logs
DROP TABLE IF EXISTS wp_wpforms_logs CASCADE;
CREATE TABLE wp_wpforms_logs (
    id BIGSERIAL NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    types VARCHAR(255) NOT NULL,
    create_at TIMESTAMP NOT NULL,
    form_id BIGINT NULL DEFAULT NULL,
    entry_id BIGINT NULL DEFAULT NULL,
    user_id BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (id)
);


-- Table: wp_wpforms_payment_meta
DROP TABLE IF EXISTS wp_wpforms_payment_meta CASCADE;
CREATE TABLE wp_wpforms_payment_meta (
    id BIGSERIAL NOT NULL,
    payment_id BIGINT NOT NULL,
    meta_key VARCHAR(255) NULL DEFAULT NULL,
    meta_value TEXT NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX payment_id ON wp_wpforms_payment_meta (payment_id);
CREATE INDEX meta_key ON wp_wpforms_payment_meta (meta_key(191);
CREATE INDEX meta_value ON wp_wpforms_payment_meta (meta_value(191);

-- Table: wp_wpforms_payments
DROP TABLE IF EXISTS wp_wpforms_payments CASCADE;
CREATE TABLE wp_wpforms_payments (
    id BIGSERIAL NOT NULL,
    form_id BIGINT NOT NULL,
    status VARCHAR(10) NOT NULL DEFAULT '',
    subtotal_amount DECIMAL(26,8) NOT NULL,
    discount_amount DECIMAL(26,8) NOT NULL,
    total_amount DECIMAL(26,8) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT '',
    entry_id BIGINT NOT NULL DEFAULT 0,
    gateway VARCHAR(20) NOT NULL DEFAULT '',
    type VARCHAR(12) NOT NULL DEFAULT '',
    mode VARCHAR(4) NOT NULL DEFAULT '',
    transaction_id VARCHAR(40) NOT NULL DEFAULT '',
    customer_id VARCHAR(40) NOT NULL DEFAULT '',
    subscription_id VARCHAR(40) NOT NULL DEFAULT '',
    subscription_status VARCHAR(10) NOT NULL DEFAULT '',
    title VARCHAR(255) NOT NULL DEFAULT '',
    date_created_gmt TIMESTAMP NOT NULL,
    date_updated_gmt TIMESTAMP NOT NULL,
    is_published BOOLEAN NOT NULL DEFAULT 1,
    PRIMARY KEY (id)
);

CREATE INDEX form_id ON wp_wpforms_payments (form_id);
CREATE INDEX status ON wp_wpforms_payments (status(8);
CREATE INDEX total_amount ON wp_wpforms_payments (total_amount);
CREATE INDEX type ON wp_wpforms_payments (type(8);
CREATE INDEX transaction_id ON wp_wpforms_payments (transaction_id(32);
CREATE INDEX customer_id ON wp_wpforms_payments (customer_id(32);
CREATE INDEX subscription_id ON wp_wpforms_payments (subscription_id(32);
CREATE INDEX subscription_status ON wp_wpforms_payments (subscription_status(8);
CREATE INDEX title ON wp_wpforms_payments (title(64);

-- Table: wp_wpforms_tasks_meta
DROP TABLE IF EXISTS wp_wpforms_tasks_meta CASCADE;
CREATE TABLE wp_wpforms_tasks_meta (
    id BIGSERIAL NOT NULL,
    action VARCHAR(255) NOT NULL,
    data TEXT NOT NULL,
    date TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);


-- Table: wp_yoast_indexable
DROP TABLE IF EXISTS wp_yoast_indexable CASCADE;
CREATE TABLE wp_yoast_indexable (
    id SERIAL NOT NULL,
    permalink TEXT NULL DEFAULT NULL,
    permalink_hash VARCHAR(40) NULL DEFAULT NULL,
    object_id BIGINT NULL DEFAULT NULL,
    object_type VARCHAR(32) NOT NULL,
    object_sub_type VARCHAR(32) NULL DEFAULT NULL,
    author_id BIGINT NULL DEFAULT NULL,
    post_parent BIGINT NULL DEFAULT NULL,
    title TEXT NULL DEFAULT NULL,
    description TEXT NULL DEFAULT NULL,
    breadcrumb_title TEXT NULL DEFAULT NULL,
    post_status VARCHAR(20) NULL DEFAULT NULL,
    is_public BOOLEAN NULL DEFAULT NULL,
    is_protected BOOLEAN DEFAULT 0,
    has_public_posts BOOLEAN NULL DEFAULT NULL,
    number_of_pages INTEGER NULL DEFAULT NULL,
    canonical TEXT NULL DEFAULT NULL,
    primary_focus_keyword VARCHAR(191) NULL DEFAULT NULL,
    primary_focus_keyword_score INTEGER NULL DEFAULT NULL,
    readability_score INTEGER NULL DEFAULT NULL,
    is_cornerstone BOOLEAN DEFAULT 0,
    is_robots_noindex BOOLEAN DEFAULT 0,
    is_robots_nofollow BOOLEAN DEFAULT 0,
    is_robots_noarchive BOOLEAN DEFAULT 0,
    is_robots_noimageindex BOOLEAN DEFAULT 0,
    is_robots_nosnippet BOOLEAN DEFAULT 0,
    twitter_title TEXT NULL DEFAULT NULL,
    twitter_image TEXT NULL DEFAULT NULL,
    twitter_description TEXT NULL DEFAULT NULL,
    twitter_image_id VARCHAR(191) NULL DEFAULT NULL,
    twitter_image_source TEXT NULL DEFAULT NULL,
    open_graph_title TEXT NULL DEFAULT NULL,
    open_graph_description TEXT NULL DEFAULT NULL,
    open_graph_image TEXT NULL DEFAULT NULL,
    open_graph_image_id VARCHAR(191) NULL DEFAULT NULL,
    open_graph_image_source TEXT NULL DEFAULT NULL,
    open_graph_image_meta TEXT NULL DEFAULT NULL,
    link_count INTEGER NULL DEFAULT NULL,
    incoming_link_count INTEGER NULL DEFAULT NULL,
    prominent_words_version INTEGER NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NOT NULL,
    blog_id BIGINT NOT NULL DEFAULT 1,
    language VARCHAR(32) NULL DEFAULT NULL,
    region VARCHAR(32) NULL DEFAULT NULL,
    schema_page_type VARCHAR(64) NULL DEFAULT NULL,
    schema_article_type VARCHAR(64) NULL DEFAULT NULL,
    has_ancestors BOOLEAN DEFAULT 0,
    estimated_reading_time_minutes INTEGER NULL DEFAULT NULL,
    version INTEGER DEFAULT 1,
    object_last_modified TIMESTAMP NULL DEFAULT NULL,
    object_published_at TIMESTAMP NULL DEFAULT NULL,
    inclusive_language_score INTEGER NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX object_type_and_sub_type ON wp_yoast_indexable (object_type,object_sub_type);
CREATE INDEX object_id_and_type ON wp_yoast_indexable (object_id,object_type);
CREATE INDEX permalink_hash_and_object_type ON wp_yoast_indexable (permalink_hash,object_type);
CREATE INDEX subpages ON wp_yoast_indexable (post_parent,object_type,post_status,object_id);
CREATE INDEX prominent_words ON wp_yoast_indexable (prominent_words_version,object_type,object_sub_type,post_status);
CREATE INDEX published_sitemap_index ON wp_yoast_indexable (object_published_at,is_robots_noindex,object_type,object_sub_type);

-- Table: wp_yoast_indexable_hierarchy
DROP TABLE IF EXISTS wp_yoast_indexable_hierarchy CASCADE;
CREATE TABLE wp_yoast_indexable_hierarchy (
    indexable_id INTEGER NOT NULL,
    ancestor_id INTEGER NOT NULL,
    depth INTEGER NULL DEFAULT NULL,
    blog_id BIGINT NOT NULL DEFAULT 1,
    PRIMARY KEY (indexable_id,ancestor_id)
);

CREATE INDEX indexable_id ON wp_yoast_indexable_hierarchy (indexable_id);
CREATE INDEX ancestor_id ON wp_yoast_indexable_hierarchy (ancestor_id);
CREATE INDEX depth ON wp_yoast_indexable_hierarchy (depth);

-- Table: wp_yoast_migrations
DROP TABLE IF EXISTS wp_yoast_migrations CASCADE;
CREATE TABLE wp_yoast_migrations (
    id SERIAL NOT NULL,
    version VARCHAR(191) NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX wp_yoast_migrations_version ON wp_yoast_migrations (version);

-- Table: wp_yoast_primary_term
DROP TABLE IF EXISTS wp_yoast_primary_term CASCADE;
CREATE TABLE wp_yoast_primary_term (
    id SERIAL NOT NULL,
    post_id BIGINT NULL DEFAULT NULL,
    term_id BIGINT NULL DEFAULT NULL,
    taxonomy VARCHAR(32) NOT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NOT NULL,
    blog_id BIGINT NOT NULL DEFAULT 1,
    PRIMARY KEY (id)
);

CREATE INDEX post_taxonomy ON wp_yoast_primary_term (post_id,taxonomy);
CREATE INDEX post_term ON wp_yoast_primary_term (post_id,term_id);

-- Table: wp_yoast_seo_links
DROP TABLE IF EXISTS wp_yoast_seo_links CASCADE;
CREATE TABLE wp_yoast_seo_links (
    id BIGSERIAL NOT NULL,
    url VARCHAR(255) NULL DEFAULT NULL,
    post_id BIGINT NULL DEFAULT NULL,
    target_post_id BIGINT NULL DEFAULT NULL,
    type VARCHAR(8) NULL DEFAULT NULL,
    indexable_id INTEGER NULL DEFAULT NULL,
    target_indexable_id INTEGER NULL DEFAULT NULL,
    height INTEGER NULL DEFAULT NULL,
    width INTEGER NULL DEFAULT NULL,
    size INTEGER NULL DEFAULT NULL,
    language VARCHAR(32) NULL DEFAULT NULL,
    region VARCHAR(32) NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX link_direction ON wp_yoast_seo_links (post_id,type);
CREATE INDEX indexable_link_direction ON wp_yoast_seo_links (indexable_id,type);

