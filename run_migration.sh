#!/bin/bash

# WordPress to Supabase Migration Script
# This script executes the SQL migration file

echo "🚀 Starting WordPress to Supabase Migration..."
echo "================================================"

# Supabase project details
PROJECT_ID="cgmlpbxwmqynmshecaqn"
SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mzg4NjM5MiwiZXhwIjoyMDY5NDYyMzkyfQ.v1LM4fFcfeMRGwdETiUaWGWM6lEVD3BkpmFTI3sp1WM"

# Function to execute SQL query
execute_sql() {
    local query="$1"
    local description="$2"
    
    echo "📝 $description..."
    
    response=$(curl -s -X POST \
        "https://api.supabase.com/v1/projects/$PROJECT_ID/database/query" \
        -H "Authorization: Bearer $SERVICE_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"query\": \"$query\"}")
    
    if echo "$response" | grep -q "error"; then
        echo "❌ Error: $response"
        return 1
    else
        echo "✅ Success!"
        return 0
    fi
}

# 1. Create Auth User
echo "👤 Creating admin user..."
AUTH_USER_SQL="INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role) VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', '\$2a\$10\$dummy.hash.for.testing.purposes', NOW(), NOW(), NOW(), '{\"provider\": \"email\", \"providers\": [\"email\"]}', '{}', false, 'authenticated') ON CONFLICT (id) DO NOTHING;"

execute_sql "$AUTH_USER_SQL" "Creating auth user"

# 2. Create User Profile
USER_PROFILE_SQL="INSERT INTO users (id, email, full_name, username, role, created_at, updated_at) VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Dhananjay', 'dhananjay', 'admin', '2023-07-20 22:58:21', NOW()) ON CONFLICT (id) DO UPDATE SET full_name = EXCLUDED.full_name, username = EXCLUDED.username, role = EXCLUDED.role, updated_at = EXCLUDED.updated_at;"

execute_sql "$USER_PROFILE_SQL" "Creating user profile"

# 3. Insert Categories (already done above, but let's make sure)
CATEGORIES_SQL="INSERT INTO categories (id, name, slug, description, created_at, updated_at) VALUES ('d9cb2030-9068-49dd-b306-8f2f3d686003', 'English', 'english', 'English quotes, wishes and content for social media and daily inspiration', NOW(), NOW()), ('6aaac1e6-d3fb-46f1-8e48-8510d439a147', 'Hindi', 'hindi', 'Hindi content including shayari, quotes and wishes', NOW(), NOW()), ('8668a078-837b-4b22-9b5a-beff5bb53dfa', 'Shayari', 'shayari', 'Beautiful Hindi Shayari collection for love, friendship, and life', NOW(), NOW()), ('81767b3c-b538-4dfb-83b9-68ef1278a1d6', 'Quotes', 'quotes', 'Inspirational and motivational quotes', NOW(), NOW()), ('102e6d17-ed66-4019-bdfc-1f147fb1859f', 'Status', 'status', 'Status updates and captions for social media', NOW(), NOW()), ('f3b7e4f5-d01f-4f9a-bd3a-6e12f55259d7', 'Jokes', 'jokes', 'Funny jokes and humor content', NOW(), NOW()), ('0ab6a315-5537-47ee-a280-56bc2aca9472', 'Poetries', 'poetries', 'Beautiful poetry and verses', NOW(), NOW()), ('a346cbbb-2f51-4cfe-b5fe-eac79836b775', 'Stories', 'stories', 'Short stories and narratives', NOW(), NOW()) ON CONFLICT (id) DO NOTHING;"

execute_sql "$CATEGORIES_SQL" "Creating categories"

echo ""
echo "🎉 Migration completed successfully!"
echo ""
echo "📊 Your Supabase database now contains:"
echo "   • 1 admin user (<EMAIL>)"
echo "   • 8 categories (English, Hindi, Shayari, Quotes, Status, Jokes, Poetries, Stories)"
echo ""
echo "🔗 Next steps:"
echo "   1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/$PROJECT_ID"
echo "   2. Check the 'Table Editor' to see your data"
echo "   3. You can now add posts manually or run additional migration scripts"
echo ""
echo "✨ Happy blogging!"
