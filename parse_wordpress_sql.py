#!/usr/bin/env python3
"""
WordPress to PostgreSQL Migration Parser
Extracts table schemas and data from WordPress SQL dump, excluding user tables
"""

import re
import sys
from typing import Dict, List, Tuple

class WordPressSQLParser:
    def __init__(self, sql_file_path: str):
        self.sql_file_path = sql_file_path
        self.excluded_tables = {'wp_users', 'wp_usermeta'}
        self.tables = {}
        self.table_data = {}

    def parse_sql_file(self):
        """Parse the SQL file and extract table structures and data"""
        print("Parsing WordPress SQL file...")

        with open(self.sql_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Find all CREATE TABLE statements
        create_table_pattern = r'CREATE TABLE `([^`]+)`\s*\((.*?)\)\s*ENGINE=.*?;'
        matches = re.findall(create_table_pattern, content, re.DOTALL | re.IGNORECASE)

        for table_name, table_definition in matches:
            if table_name not in self.excluded_tables:
                self.tables[table_name] = self.parse_table_definition(table_definition)
                print(f"Found table: {table_name}")

        # Extract INSERT statements for each table
        for table_name in self.tables.keys():
            insert_pattern = rf'INSERT INTO `{re.escape(table_name)}` VALUES\s*(.*?);'
            insert_matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)

            if insert_matches:
                self.table_data[table_name] = insert_matches[0]
                print(f"Found data for table: {table_name}")

        print(f"Parsed {len(self.tables)} tables (excluding user tables)")
        return self.tables, self.table_data

    def parse_table_definition(self, definition: str) -> Dict:
        """Parse table definition to extract columns and constraints"""
        lines = [line.strip() for line in definition.split('\n') if line.strip()]

        columns = []
        indexes = []
        primary_key = None

        for line in lines:
            line = line.rstrip(',')

            if line.startswith('`') and not line.startswith('PRIMARY KEY') and not line.startswith('KEY'):
                # Column definition
                columns.append(line)
            elif line.startswith('PRIMARY KEY'):
                primary_key = line
            elif line.startswith('KEY') or line.startswith('UNIQUE KEY'):
                indexes.append(line)

        return {
            'columns': columns,
            'primary_key': primary_key,
            'indexes': indexes
        }

    def mysql_to_postgresql_type(self, mysql_type: str) -> str:
        """Convert MySQL data types to PostgreSQL equivalents"""
        mysql_type = mysql_type.strip()

        # Handle varchar with variable lengths
        varchar_match = re.match(r'varchar\((\d+)\)', mysql_type, re.IGNORECASE)
        if varchar_match:
            return f'VARCHAR({varchar_match.group(1)})'

        # Handle tinyint
        if mysql_type.lower().startswith('tinyint'):
            if 'tinyint(1)' in mysql_type.lower():
                return 'BOOLEAN'
            else:
                return 'SMALLINT'

        # Handle other numeric types
        if mysql_type.lower().startswith('bigint'):
            return 'BIGINT'
        elif mysql_type.lower().startswith('int'):
            return 'INTEGER'
        elif mysql_type.lower().startswith('decimal'):
            decimal_match = re.match(r'decimal\((\d+),(\d+)\)', mysql_type, re.IGNORECASE)
            if decimal_match:
                return f'DECIMAL({decimal_match.group(1)},{decimal_match.group(2)})'
            return 'DECIMAL'
        elif mysql_type.lower() == 'float':
            return 'REAL'
        elif mysql_type.lower() == 'double':
            return 'DOUBLE PRECISION'
        elif mysql_type.lower() in ['text', 'longtext', 'mediumtext']:
            return 'TEXT'
        elif mysql_type.lower() == 'datetime':
            return 'TIMESTAMP'
        elif mysql_type.lower() == 'date':
            return 'DATE'
        elif mysql_type.lower() == 'time':
            return 'TIME'

        return mysql_type.upper()

    def generate_postgresql_schema(self) -> str:
        """Generate PostgreSQL CREATE TABLE statements"""
        schema_sql = "-- WordPress to PostgreSQL Migration Schema\n"
        schema_sql += "-- Generated automatically, excluding wp_users and wp_usermeta\n\n"

        for table_name, table_def in self.tables.items():
            schema_sql += f"-- Table: {table_name}\n"
            schema_sql += f"DROP TABLE IF EXISTS {table_name} CASCADE;\n"
            schema_sql += f"CREATE TABLE {table_name} (\n"

            # Process columns
            column_definitions = []
            for column in table_def['columns']:
                pg_column = self.convert_column_to_postgresql(column)
                column_definitions.append(f"    {pg_column}")

            schema_sql += ",\n".join(column_definitions)

            # Add primary key
            if table_def['primary_key']:
                pk_def = self.convert_primary_key_to_postgresql(table_def['primary_key'])
                schema_sql += f",\n    {pk_def}"

            schema_sql += "\n);\n\n"

            # Add indexes
            for index in table_def['indexes']:
                index_sql = self.convert_index_to_postgresql(table_name, index)
                if index_sql:
                    schema_sql += f"{index_sql}\n"

            schema_sql += "\n"

        return schema_sql

    def convert_column_to_postgresql(self, mysql_column: str) -> str:
        """Convert MySQL column definition to PostgreSQL"""
        # Parse column definition
        parts = mysql_column.split()
        column_name = parts[0].strip('`')

        # Extract data type
        type_part = parts[1]
        if '(' in type_part and ')' in type_part:
            base_type = type_part
        else:
            # Handle multi-word types
            type_parts = []
            for i in range(1, len(parts)):
                if parts[i].upper() in ['NOT', 'NULL', 'DEFAULT', 'AUTO_INCREMENT']:
                    break
                type_parts.append(parts[i])
            base_type = ' '.join(type_parts)

        pg_type = self.mysql_to_postgresql_type(base_type)

        # Handle constraints
        constraints = []
        mysql_def = ' '.join(parts[2:]) if len(parts) > 2 else ''

        if 'NOT NULL' in mysql_def.upper():
            constraints.append('NOT NULL')
        elif 'NULL' in mysql_def.upper() and 'NOT NULL' not in mysql_def.upper():
            constraints.append('NULL')

        if 'AUTO_INCREMENT' in mysql_def.upper():
            if 'BIGINT' in pg_type.upper():
                pg_type = 'BIGSERIAL'
            else:
                pg_type = 'SERIAL'

        # Handle DEFAULT values
        default_match = re.search(r"DEFAULT\s+([^,\s]+(?:\s+[^,\s]+)*)", mysql_def, re.IGNORECASE)
        if default_match:
            default_value = default_match.group(1).strip()
            if default_value != "'0000-00-00 00:00:00'":  # Skip invalid MySQL datetime
                if default_value.isdigit():
                    constraints.append(f'DEFAULT {default_value}')
                elif default_value.startswith("'") and default_value.endswith("'"):
                    constraints.append(f'DEFAULT {default_value}')
                elif default_value.upper() == 'NULL':
                    constraints.append('DEFAULT NULL')

        result = f"{column_name} {pg_type}"
        if constraints:
            result += " " + " ".join(constraints)

        return result

    def convert_primary_key_to_postgresql(self, mysql_pk: str) -> str:
        """Convert MySQL PRIMARY KEY to PostgreSQL"""
        # Extract column names from PRIMARY KEY (`col1`, `col2`)
        match = re.search(r'PRIMARY KEY \(([^)]+)\)', mysql_pk)
        if match:
            columns = match.group(1).replace('`', '').strip()
            return f"PRIMARY KEY ({columns})"
        return ""

    def convert_index_to_postgresql(self, table_name: str, mysql_index: str) -> str:
        """Convert MySQL index to PostgreSQL"""
        # Parse KEY `index_name` (`column1`, `column2`)
        key_match = re.match(r'(UNIQUE\s+)?KEY\s+`([^`]+)`\s+\(([^)]+)\)', mysql_index)
        if key_match:
            is_unique = key_match.group(1) is not None
            index_name = key_match.group(2)
            columns = key_match.group(3).replace('`', '')

            # Handle column length specifications like `column`(191) - remove them for PostgreSQL
            columns = re.sub(r'([a-zA-Z_][a-zA-Z0-9_]*)\(\d+\)', r'\1', columns)

            unique_clause = "UNIQUE " if is_unique else ""
            return f"CREATE {unique_clause}INDEX {index_name} ON {table_name} ({columns});"

        return ""

if __name__ == "__main__":
    parser = WordPressSQLParser("u957990218_GpBKT.zayotech-com.20250727190356.sql")
    tables, data = parser.parse_sql_file()

    # Generate PostgreSQL schema
    schema = parser.generate_postgresql_schema()

    # Save schema to file
    with open("wordpress_schema_postgresql.sql", "w", encoding="utf-8") as f:
        f.write(schema)

    print(f"\nGenerated PostgreSQL schema saved to: wordpress_schema_postgresql.sql")
    print(f"Total tables to migrate: {len(tables)}")
    print("Excluded tables: wp_users, wp_usermeta")