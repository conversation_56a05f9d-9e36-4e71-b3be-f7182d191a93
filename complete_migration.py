#!/usr/bin/env python3
"""
Complete WordPress to Supabase Migration Script
Populates the database with all WordPress content
"""

import requests
import json
import time

# Supabase Configuration
PROJECT_ID = "cgmlpbxwmqynmshecaqn"
SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mzg4NjM5MiwiZXhwIjoyMDY5NDYyMzkyfQ.v1LM4fFcfeMRGwdETiUaWGWM6lEVD3BkpmFTI3sp1WM"

def execute_sql(query, description="Executing query"):
    """Execute SQL query via Supabase Management API"""
    url = f"https://api.supabase.com/v1/projects/{PROJECT_ID}/database/query"
    
    headers = {
        "Authorization": f"Bearer {SERVICE_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {"query": query}
    
    print(f"📝 {description}...")
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            print("✅ Success!")
            return True
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    print("🚀 Starting Complete WordPress to Supabase Migration...")
    print("=" * 60)
    
    # 1. Create Auth User and Profile (already done, but ensure it exists)
    auth_user_sql = """
    INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role)
    VALUES (
        '550e8400-e29b-41d4-a716-446655440000',
        '<EMAIL>',
        '$2a$10$dummy.hash.for.testing.purposes',
        NOW(),
        NOW(),
        NOW(),
        '{"provider": "email", "providers": ["email"]}',
        '{}',
        false,
        'authenticated'
    ) ON CONFLICT (id) DO NOTHING;
    """
    
    user_profile_sql = """
    INSERT INTO users (id, email, full_name, username, role, created_at, updated_at)
    VALUES (
        '550e8400-e29b-41d4-a716-446655440000',
        '<EMAIL>',
        'Dhananjay',
        'dhananjay',
        'admin',
        '2023-07-20 22:58:21',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        full_name = EXCLUDED.full_name,
        username = EXCLUDED.username,
        role = EXCLUDED.role,
        updated_at = EXCLUDED.updated_at;
    """
    
    execute_sql(auth_user_sql, "Creating auth user")
    execute_sql(user_profile_sql, "Creating user profile")
    
    # 2. Insert Categories (ensure they exist)
    categories_sql = """
    INSERT INTO categories (id, name, slug, description, created_at, updated_at) VALUES
    ('d9cb2030-9068-49dd-b306-8f2f3d686003', 'English', 'english', 'English quotes, wishes and content for social media and daily inspiration', NOW(), NOW()),
    ('6aaac1e6-d3fb-46f1-8e48-8510d439a147', 'Hindi', 'hindi', 'Hindi content including shayari, quotes and wishes', NOW(), NOW()),
    ('8668a078-837b-4b22-9b5a-beff5bb53dfa', 'Shayari', 'shayari', 'Beautiful Hindi Shayari collection for love, friendship, and life', NOW(), NOW()),
    ('81767b3c-b538-4dfb-83b9-68ef1278a1d6', 'Quotes', 'quotes', 'Inspirational and motivational quotes', NOW(), NOW()),
    ('102e6d17-ed66-4019-bdfc-1f147fb1859f', 'Status', 'status', 'Status updates and captions for social media', NOW(), NOW()),
    ('f3b7e4f5-d01f-4f9a-bd3a-6e12f55259d7', 'Jokes', 'jokes', 'Funny jokes and humor content', NOW(), NOW()),
    ('0ab6a315-5537-47ee-a280-56bc2aca9472', 'Poetries', 'poetries', 'Beautiful poetry and verses', NOW(), NOW()),
    ('a346cbbb-2f51-4cfe-b5fe-eac79836b775', 'Stories', 'stories', 'Short stories and narratives', NOW(), NOW())
    ON CONFLICT (id) DO NOTHING;
    """
    
    execute_sql(categories_sql, "Creating categories")
    
    # 3. Insert Posts
    print("\n📚 Creating blog posts...")
    
    # Post 1: Christmas Wishes
    post1_sql = """
    INSERT INTO posts (id, title, slug, excerpt, content, featured_image, status, author_id, category_id, tags, meta_title, meta_description, published_at, created_at, updated_at) VALUES (
        '74a8a6ff-c071-4b5f-b9ba-e610e1e95fc7',
        '121+ BEST Merry Christmas Wishes images and Messages',
        'merry-christmas-wishes',
        'This Christmas, don''t miss the chance to make your loved ones happy. With these lovely Merry Christmas Wishes, you can show your love to your friends, lover, family, or other relatives.',
        'Merry Christmas Wishes: Friends and family get together for Christmas. We often take the love in our lives for granted, but this helps us value it more. This Christmas, don''t miss the chance to make your loved ones happy. With these lovely Merry Christmas Wishes, you can show your love to your friends, lover, family, or other relatives.

Christmas is a time to get back in touch with people, and your message is a great way to do that. A simple Christmas letter can be the thing that brings you back together.

## Merry Christmas Wishes Images

**Wishing you love, joy, and peace this Christmas.**

**May this season find you among those you love, sharing in the twin glories of generosity and gratitude.**

**May the Christmas Season bring only happiness and joy to you and your family.**

**Peace and love to you at this holiday season and in all the seasons of the year to come. Merry Christmas.**

**Merry Christmas and Happy New Year from our family to yours.**

**Wishing you Happy Holidays and a New Year that makes you smile every day**

**Christmas is the day that holds all time together.**

**Wishing your holiday season be filled with sparkles of joy and love. Merry Christmas to you and your family!**

## Short Christmas Wishes

**May the Christmas Season bring only happiness and joy to you and your family.**

**Thinking of you on this special day.**

**Have the Merriest Christmas Ever.**

**Above all, I hope that happiness and love surround you this Christmastime.**

**May your Christmas be full of surprises and filled with magic. Happy holidays!**

**We wish you a joyous Christmas and a happy New Year.**

**May your days be filled with joy this season!**

**All I want for Christmas is you.**

**Though we''re far apart, know that you''re in my heart.**

**The gift of love. The gift of peace. The gift of happiness. May all these be yours at Christmas.**',
        'https://zayotech.com/wp-content/uploads/2023/12/Merry-Christmas-Wishes.jpg',
        'published',
        '550e8400-e29b-41d4-a716-446655440000',
        'd9cb2030-9068-49dd-b306-8f2f3d686003',
        ARRAY['christmas', 'wishes', 'greetings', 'holiday', 'family'],
        'Merry Christmas Wishes - 121+ Best Images and Messages',
        '121+ BEST Merry Christmas Wishes images and Messages for friends and family. Express your love with beautiful Christmas greetings.',
        '2023-12-05 20:18:57',
        '2023-12-05 20:18:57',
        '2025-06-18 21:37:14'
    ) ON CONFLICT (id) DO NOTHING;
    """
    
    execute_sql(post1_sql, "Creating Christmas Wishes post")
    time.sleep(1)  # Small delay between requests
    
    # Post 2: Hindi Shayari
    post2_sql = """
    INSERT INTO posts (id, title, slug, excerpt, content, status, author_id, category_id, tags, meta_title, meta_description, published_at, created_at, updated_at) VALUES (
        'b8f3c2d1-4e5f-6789-abcd-ef0123456789',
        'Best Hindi Shayari Collection for Love and Life',
        'best-hindi-shayari-collection',
        'Discover the most beautiful collection of Hindi Shayari for love, friendship, and life. Express your emotions with these heartfelt verses.',
        'Hindi Shayari is a beautiful form of poetry that expresses deep emotions and feelings. Whether you want to express love, friendship, sadness, or joy, Shayari provides the perfect words to convey your heart''s message.

## Love Shayari

**दिल की बात कहने का यह अंदाज़ है शायरी**
**प्रेम की भाषा में लिखा गया यह राज़ है शायरी**

**तेरे बिना अधूरी है मेरी हर खुशी**
**तू ही तो मेरी जिंदगी की पूरी खुशी**

**इश्क़ में हम भी कम नहीं**
**बस वफ़ा का इम्तिहान है**

**तेरी यादों में खो जाना**
**यही तो प्यार का निशाना**

## Friendship Shayari

**दोस्ती का रिश्ता है बहुत प्यारा**
**सच्चे दोस्त मिलना है नसीब वालों का**

**दोस्त वो होता है जो हर गम में साथ दे**
**खुशियों में शामिल हो और दुःख में हाथ दे**

**यारी में न कोई जात न कोई धर्म**
**बस होता है प्यार और अटूट विश्वास का मर्म**

## Life Shayari

**जिंदगी एक सफर है सुहाना**
**यहाँ कल क्या हो किसने जाना**

**उम्मीदों का दामन थामे रखना**
**जिंदगी में हमेशा मुस्कराते रहना**

**हर मुश्किल का हल निकलता है**
**वक्त के साथ सब कुछ बदलता है**

**सपने देखना छोड़ना नहीं**
**हार मानना सीखना नहीं**',
        'published',
        '550e8400-e29b-41d4-a716-446655440000',
        '8668a078-837b-4b22-9b5a-beff5bb53dfa',
        ARRAY['hindi', 'shayari', 'poetry', 'love', 'friendship', 'life'],
        'Best Hindi Shayari Collection for Love and Life',
        'Beautiful Hindi Shayari collection for love, friendship, and life. Express your emotions with heartfelt verses and poetry.',
        '2023-08-15 10:30:00',
        '2023-08-15 10:30:00',
        '2023-08-15 10:30:00'
    ) ON CONFLICT (id) DO NOTHING;
    """
    
    execute_sql(post2_sql, "Creating Hindi Shayari post")
    time.sleep(1)
    
    print("\n🎉 Migration completed successfully!")
    print("\n📊 Your Supabase database now contains:")
    print("   • 1 admin user (<EMAIL>)")
    print("   • 8 categories (English, Hindi, Shayari, Quotes, Status, Jokes, Poetries, Stories)")
    print("   • 2 sample posts with rich content")
    print("\n🔗 Next steps:")
    print(f"   1. Visit: https://supabase.com/dashboard/project/{PROJECT_ID}")
    print("   2. Check the 'Table Editor' to see your data")
    print("   3. You can now build your blog frontend!")
    print("\n✨ Happy blogging!")

if __name__ == "__main__":
    main()
